{"name": "tarn", "version": "3.0.2", "description": "Simple and robust resource pool for node.js", "main": "dist/tarn.js", "types": "dist/tarn.d.ts", "license": "MIT", "scripts": {"test": "mocha --slow 10 --timeout 5000 --reporter spec tests.js", "test-bail": "mocha --slow 10 --timeout 5000 --reporter spec --bail tests.js", "build": "tsc", "clean": "rm -rf dist", "prepublishOnly": "tsc", "lint": "eslint --format codeframe \"src/**/*.ts\"", "format": "prettier **/*.{js,ts} --write"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/koskimas"}, "repository": {"type": "git", "url": "git://github.com/vincit/tarn.js.git"}, "engines": {"node": ">=8.0.0"}, "keywords": ["pool", "pooling", "throttle"], "files": ["README.md", "LICENSE", "dist/*"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts}": ["prettier --write", "git add"]}, "devDependencies": {"@types/node": "^10.17.17", "@typescript-eslint/eslint-plugin": "^2.21.0", "@typescript-eslint/parser": "^2.21.0", "bluebird": "^3.7.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "expect.js": "^0.3.1", "husky": "^1.3.1", "lint-staged": "^9.5.0", "mocha": "^7.1.0", "prettier": "^1.19.1", "typescript": "3.8.3"}, "dependencies": {}}