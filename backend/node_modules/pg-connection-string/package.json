{"name": "pg-connection-string", "version": "2.6.1", "description": "Functions for dealing with a PostgresSQL connection string", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "istanbul cover _mocha && npm run check-coverage", "check-coverage": "istanbul check-coverage --statements 100 --branches 100 --lines 100 --functions 100", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-connection-string"}, "keywords": ["pg", "connection", "string", "parse"], "author": "<PERSON> <<EMAIL>> (http://iceddev.com/)", "license": "MIT", "bugs": {"url": "https://github.com/brianc/node-postgres/issues"}, "homepage": "https://github.com/brianc/node-postgres/tree/master/packages/pg-connection-string", "devDependencies": {"chai": "^4.1.1", "coveralls": "^3.0.4", "istanbul": "^0.4.5", "mocha": "^7.1.2"}, "files": ["index.js", "index.d.ts"], "gitHead": "eaafac36dc8f4a13f1fecc9e3420d35559fd8e2b"}