{"name": "tildify", "version": "2.0.0", "description": "Convert an absolute path to a tilde path: `/Users/<USER>/dev` → `~/dev`", "license": "MIT", "repository": "sindresorhus/tildify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["unexpand", "homedir", "tilde", "tildify", "collapse", "path", "home", "directory", "user", "expand", "convert"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}