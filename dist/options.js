(()=>{class t{constructor(){this.init()}async init(){this.bindEvents(),await this.checkAuthStatus(),await this.loadSubmissionHistory(),this.loadExtensionInfo()}bindEvents(){document.getElementById("login-btn").addEventListener("click",()=>this.handleLogin()),document.getElementById("logout-btn").addEventListener("click",()=>this.handleLogout()),document.getElementById("refresh-history").addEventListener("click",()=>this.loadSubmissionHistory()),document.getElementById("clear-history").addEventListener("click",()=>this.clearHistory())}async checkAuthStatus(){try{const t=await this.sendMessage({action:"checkAuth"});t.authenticated&&t.profile?this.showAuthenticatedState(t.profile):this.showUnauthenticatedState()}catch(t){console.error("Failed to check auth status:",t),this.showUnauthenticatedState()}}showAuthenticatedState(t){document.getElementById("auth-status").style.display="none",document.getElementById("auth-details").style.display="block",document.getElementById("auth-required").style.display="none",document.getElementById("user-name").textContent=`${t.localizedFirstName} ${t.localizedLastName}`,document.getElementById("user-email").textContent="LinkedIn Account"}showUnauthenticatedState(){document.getElementById("auth-status").style.display="none",document.getElementById("auth-details").style.display="none",document.getElementById("auth-required").style.display="block"}async handleLogin(){try{const t=document.getElementById("login-btn");t.disabled=!0,t.textContent="Authenticating...";const e=await this.sendMessage({action:"authenticate"});if(!e.success)throw new Error(e.error||"Authentication failed");this.showAuthenticatedState(e.profile),this.showStatusMessage("Successfully authenticated with LinkedIn!","success")}catch(t){this.showStatusMessage("Authentication failed: "+t.message,"error")}finally{const t=document.getElementById("login-btn");t.disabled=!1,t.textContent="Login with LinkedIn"}}async handleLogout(){try{await this.sendMessage({action:"logout"}),this.showUnauthenticatedState(),this.showStatusMessage("Successfully logged out","info")}catch(t){this.showStatusMessage("Logout failed: "+t.message,"error")}}async loadSubmissionHistory(){try{const t=document.getElementById("submission-history");t.innerHTML='<div class="history-loading"><div class="spinner"></div><span>Loading history...</span></div>';const e=(await this.sendMessage({action:"getSubmissionHistory"})).history||[];if(0===e.length)return void(t.innerHTML='<div class="history-empty">No submissions yet. Start by saving a job from LinkedIn!</div>');t.innerHTML="",e.forEach(e=>{const s=this.createHistoryItem(e);t.appendChild(s)})}catch(t){document.getElementById("submission-history").innerHTML='<div class="history-empty">Failed to load history</div>'}}createHistoryItem(t){const e=document.createElement("div");e.className="history-item "+(t.success?"success":"error");const s=new Date(t.submitted_at).toLocaleString();return e.innerHTML=`\n      <div class="history-item-header">\n        <div>\n          <div class="history-item-title">${t.job_title}</div>\n          <div class="history-item-company">${t.company_name}</div>\n        </div>\n        <div>\n          <div class="history-item-time">${s}</div>\n          <div class="history-item-status ${t.success?"success":"error"}">\n            ${t.success?"✓ Success":"✗ Failed"}\n          </div>\n        </div>\n      </div>\n      ${t.error?`<div class="history-item-error">${t.error}</div>`:""}\n    `,e}async clearHistory(){if(confirm("Are you sure you want to clear all submission history? This action cannot be undone."))try{await chrome.storage.local.remove("submission_history"),await this.loadSubmissionHistory(),this.showStatusMessage("History cleared successfully","info")}catch(t){this.showStatusMessage("Failed to clear history: "+t.message,"error")}}loadExtensionInfo(){const t=chrome.runtime.getManifest();document.getElementById("extension-version").textContent=t.version,document.getElementById("last-updated").textContent=(new Date).toLocaleDateString()}isValidUrl(t){try{return new URL(t),!0}catch(t){return!1}}showStatusMessage(t,e="info"){const s=document.getElementById("status-messages"),n=document.createElement("div");n.className=`status-message ${e}`,n.textContent=t,s.appendChild(n),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n)},5e3)}async sendMessage(t){return new Promise(e=>{chrome.runtime.sendMessage(t,e)})}}document.addEventListener("DOMContentLoaded",()=>{new t})})();