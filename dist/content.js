(()=>{const t=new class{constructor(){this.jobData=null,this.isJobPage=this.detectJobPage()}detectJobPage(){const t=window.location.href;return t.includes("linkedin.com/jobs/view/")&&t.match(/\/jobs\/view\/\d+/)}extractJobId(){const t=window.location.href.match(/\/jobs\/view\/(\d+)/);return t?t[1]:null}extractJobTitle(){return this.getTextFromSelectors([".top-card-layout__title",".job-details-jobs-unified-top-card__job-title h1",'h1[data-test-id="job-title"]',".jobs-unified-top-card__job-title h1"])}extractCompanyInfo(){const t={name:"",logo_url:"",linkedin_url:""},e=this.getElementFromSelectors([".top-card-layout__card .top-card-layout__entity-info a",".job-details-jobs-unified-top-card__company-name a",".jobs-unified-top-card__company-name a"]);e&&(t.name=e.textContent.trim(),t.linkedin_url=e.href);const o=this.getElementFromSelectors([".top-card-layout__card .top-card-layout__entity-info img",".job-details-jobs-unified-top-card__company-logo img",".jobs-unified-top-card__company-logo img"]);return o&&(t.logo_url=o.src),t}extractLocation(){const t=this.getTextFromSelectors([".top-card-layout__card .top-card-layout__second-subline",".job-details-jobs-unified-top-card__primary-description-container .tvm__text--low-emphasis",".jobs-unified-top-card__bullet"]);return t?t.split("·")[0].trim():""}extractWorkplaceType(){const t=this.getTextFromSelectors([".top-card-layout__card .top-card-layout__second-subline",".job-details-jobs-unified-top-card__primary-description-container"]);if(t){if(t.toLowerCase().includes("remote"))return"Remote";if(t.toLowerCase().includes("hybrid"))return"Hybrid";if(t.toLowerCase().includes("on-site")||t.toLowerCase().includes("onsite"))return"Onsite"}return"Onsite"}extractEmploymentType(){const t=document.querySelectorAll([".job-details-jobs-unified-top-card__job-insight span",".jobs-unified-top-card__job-insight span"].join(", "));for(const e of t){const t=e.textContent.toLowerCase();if(t.includes("full-time"))return"Full-time";if(t.includes("part-time"))return"Part-time";if(t.includes("contract"))return"Contract";if(t.includes("temporary"))return"Temporary";if(t.includes("internship"))return"Internship"}return"Full-time"}extractPostedTime(){const t=document.querySelectorAll([".job-details-jobs-unified-top-card__primary-description-container .tvm__text--low-emphasis",".jobs-unified-top-card__subtitle-primary-grouping .tvm__text--low-emphasis"].join(", "));for(const e of t){const t=e.textContent;if(t.includes("ago")||t.includes("hour")||t.includes("day")||t.includes("week"))return t.trim()}return""}extractApplicants(){const t=document.querySelectorAll([".job-details-jobs-unified-top-card__primary-description-container .tvm__text--low-emphasis",".jobs-unified-top-card__subtitle-primary-grouping .tvm__text--low-emphasis"].join(", "));for(const e of t){const t=e.textContent;if(t.includes("applicant"))return t.trim()}return""}extractApplyLink(){return window.location.href}extractEasyApply(){return!!document.querySelector('.jobs-apply-button--top-card, .jobs-s-apply button[aria-label*="Easy Apply"]')}extractJobDescription(){const t={summary:"",responsibilities:[],requirements:[],benefits:[]},e=document.querySelector(".jobs-description-content__text, .jobs-box__html-content");if(!e)return t;const o=e.textContent;return t.summary=this.extractSummary(o),t.responsibilities=this.extractSection(o,["responsibilities","duties","role"]),t.requirements=this.extractSection(o,["requirements","qualifications","skills"]),t.benefits=this.extractSection(o,["benefits","perks","compensation"]),t}extractSummary(t){return t.split(".").slice(0,3).join(".").trim()}extractSection(t,e){const o=t.split("\n").map(t=>t.trim()).filter(t=>t.length>0),i=[];let r=!1;for(const t of o){const o=t.toLowerCase();if(e.some(t=>o.includes(t)))r=!0;else{if(r&&(o.includes("requirements")||o.includes("responsibilities")||o.includes("benefits")||o.includes("qualifications"))&&!e.some(t=>o.includes(t)))break;if(r&&t.length>10&&(i.push(t),i.length>=10))break}}return i}extractBenefitsFound(){const t=[];return document.querySelectorAll([".job-details-jobs-unified-top-card__job-insight",".jobs-unified-top-card__job-insight"].join(", ")).forEach(e=>{const o=e.textContent.toLowerCase();(o.includes("health")||o.includes("medical"))&&t.push("Health Insurance"),o.includes("dental")&&t.push("Dental Insurance"),o.includes("vision")&&t.push("Vision Insurance"),(o.includes("401k")||o.includes("retirement"))&&t.push("401k"),(o.includes("pto")||o.includes("paid time off"))&&t.push("Paid Time Off")}),[...new Set(t)]}extractPeopleYouCanReach(){const t=[],e=document.querySelector(".jobs-company-employees, .job-details-how-you-match__skills-item");return e&&e.querySelectorAll(".job-details-how-you-match__person-item, .jobs-company-employees__person").forEach(e=>{const o={name:"",linkedin_url:"",connection_degree:"",title:"",company_alum:"",profile_image:""},i=e.querySelector('a[href*="/in/"]');i&&(o.name=i.textContent.trim(),o.linkedin_url=i.href);const r=e.querySelector(".job-details-how-you-match__person-title, .jobs-company-employees__person-title");r&&(o.title=r.textContent.trim());const n=e.querySelector("img");n&&(o.profile_image=n.src);const s=e.querySelector(".job-details-how-you-match__person-degree");s&&(o.connection_degree=s.textContent.trim()),o.name&&t.push(o)}),t}extractJobPosterDetail(){const t={name:"",title:"",linkedin_url:"",profile_image:""},e=document.querySelector(".hirer-card__hirer-information, .job-details-jobs-unified-top-card__hirer-info");if(e){const o=e.querySelector('a[href*="/in/"]');o&&(t.name=o.textContent.trim(),t.linkedin_url=o.href);const i=e.querySelector(".hirer-card__hirer-information-title, .job-details-jobs-unified-top-card__hirer-title");i&&(t.title=i.textContent.trim());const r=e.querySelector("img");r&&(t.profile_image=r.src)}return t}getTextFromSelectors(t){for(const e of t){const t=document.querySelector(e);if(t)return t.textContent.trim()}return""}getElementFromSelectors(t){for(const e of t){const t=document.querySelector(e);if(t)return t}return null}extractJobData(){return this.isJobPage?(this.jobData={job_id:this.extractJobId(),job_title:this.extractJobTitle(),company:this.extractCompanyInfo(),location:this.extractLocation(),workplace_type:this.extractWorkplaceType(),employment_type:this.extractEmploymentType(),posted_time:this.extractPostedTime(),applicants:this.extractApplicants(),apply_link:this.extractApplyLink(),easy_apply:this.extractEasyApply(),job_description:this.extractJobDescription(),benefits_found:this.extractBenefitsFound(),people_you_can_reach:this.extractPeopleYouCanReach(),job_poster_detail:this.extractJobPosterDetail()},this.jobData):null}};chrome.runtime.onMessage.addListener((e,o,i)=>("extractJobData"===e.action&&i({jobData:t.extractJobData()}),!0)),t.isJobPage&&setTimeout(()=>{t.extractJobData()},2e3)})();