{"version": 3, "file": "background.js", "mappings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sources": ["webpack://job-tracker-extension/./src/background.js"], "sourcesContent": ["// Background Service Worker for Job Tracker Extension\n\n// Import security manager\nimportScripts('security.js');\n\n// Simple encryption/decryption using built-in crypto\nconst CryptoJS = {\n  AES: {\n    encrypt: (data, key) => {\n      // Simple base64 encoding for demo - in production use proper encryption\n      return btoa(JSON.stringify({ data, key: key.substring(0, 8) }));\n    },\n    decrypt: (encryptedData, key) => {\n      try {\n        const decoded = JSON.parse(atob(encryptedData));\n        if (decoded.key === key.substring(0, 8)) {\n          return { toString: () => decoded.data };\n        }\n        return null;\n      } catch (error) {\n        return null;\n      }\n    }\n  },\n  enc: {\n    Utf8: {}\n  }\n};\n\nclass AuthManager {\n  constructor() {\n    // LinkedIn Social Login - no client ID needed for public social login\n    this.clientId = '772ojq74j5zy0p'; // LinkedIn's public social login client ID\n    this.redirectUri = chrome.identity.getRedirectURL();\n    this.scope = 'r_liteprofile r_emailaddress';\n    this.encryptionKey = 'job-tracker-secret-key'; // In production, generate this dynamically\n    this.security = new SecurityManager();\n\n    // LinkedIn OAuth endpoints\n    this.authUrl = 'https://www.linkedin.com/oauth/v2/authorization';\n    this.tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';\n    this.profileUrl = 'https://api.linkedin.com/v2/people/~';\n  }\n\n  // Encrypt sensitive data before storage\n  encrypt(data) {\n    return CryptoJS.AES.encrypt(JSON.stringify(data), this.encryptionKey).toString();\n  }\n\n  // Decrypt sensitive data after retrieval\n  decrypt(encryptedData) {\n    try {\n      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);\n      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));\n    } catch (error) {\n      console.error('Decryption failed:', error);\n      return null;\n    }\n  }\n\n  // Initiate LinkedIn Social Login flow\n  async authenticate() {\n    const state = this.generateState();\n    const authUrl = `${this.authUrl}?` +\n      `response_type=code&` +\n      `client_id=${this.clientId}&` +\n      `redirect_uri=${encodeURIComponent(this.redirectUri)}&` +\n      `scope=${encodeURIComponent(this.scope)}&` +\n      `state=${state}`;\n\n    try {\n      const responseUrl = await chrome.identity.launchWebAuthFlow({\n        url: authUrl,\n        interactive: true\n      });\n\n      const urlParams = new URLSearchParams(new URL(responseUrl).search);\n      const code = urlParams.get('code');\n      const returnedState = urlParams.get('state');\n\n      // Validate state parameter for security\n      if (!this.security.validateState(returnedState, state)) {\n        throw new Error('Invalid state parameter - possible CSRF attack');\n      }\n\n      if (code) {\n        const tokens = await this.exchangeCodeForTokens(code);\n        await this.storeTokens(tokens);\n        const profile = await this.fetchUserProfile(tokens.access_token);\n        await this.storeUserProfile(profile);\n        return { success: true, profile };\n      } else {\n        throw new Error('No authorization code received');\n      }\n    } catch (error) {\n      console.error('LinkedIn Social Login failed:', error);\n      return { success: false, error: error.message };\n    }\n  }\n\n  // Generate random state for OAuth security\n  generateState() {\n    return this.security.generateSecureState();\n  }\n\n  // Extract authorization code from redirect URL\n  extractCodeFromUrl(url) {\n    const urlParams = new URLSearchParams(new URL(url).search);\n    return urlParams.get('code');\n  }\n\n  // Exchange authorization code for access tokens using LinkedIn Social Login\n  async exchangeCodeForTokens(code) {\n    // For LinkedIn Social Login, we use a simplified token exchange\n    // that doesn't require a client secret for public social login\n    const params = new URLSearchParams({\n      grant_type: 'authorization_code',\n      code: code,\n      redirect_uri: this.redirectUri,\n      client_id: this.clientId\n      // No client_secret needed for LinkedIn Social Login\n    });\n\n    const response = await fetch(this.tokenUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n        'Accept': 'application/json'\n      },\n      body: params\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error('Token exchange failed:', errorText);\n      throw new Error(`Token exchange failed: ${response.status}`);\n    }\n\n    const tokens = await response.json();\n\n    // Validate token response\n    if (!tokens.access_token) {\n      throw new Error('No access token received from LinkedIn');\n    }\n\n    return tokens;\n  }\n\n  // Fetch user profile from LinkedIn API\n  async fetchUserProfile(accessToken) {\n    try {\n      // Fetch basic profile information\n      const profileResponse = await fetch(this.profileUrl, {\n        headers: {\n          'Authorization': `Bearer ${accessToken}`,\n          'Accept': 'application/json'\n        }\n      });\n\n      if (!profileResponse.ok) {\n        throw new Error(`Profile fetch failed: ${profileResponse.status}`);\n      }\n\n      const profile = await profileResponse.json();\n\n      // Fetch email address separately (different endpoint)\n      const emailResponse = await fetch('https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))', {\n        headers: {\n          'Authorization': `Bearer ${accessToken}`,\n          'Accept': 'application/json'\n        }\n      });\n\n      let email = null;\n      if (emailResponse.ok) {\n        const emailData = await emailResponse.json();\n        if (emailData.elements && emailData.elements.length > 0) {\n          email = emailData.elements[0]['handle~'].emailAddress;\n        }\n      }\n\n      // Combine profile and email data\n      return {\n        ...profile,\n        email: email\n      };\n    } catch (error) {\n      console.error('Failed to fetch LinkedIn profile:', error);\n      throw new Error('Failed to fetch user profile from LinkedIn');\n    }\n  }\n\n  // Store encrypted tokens\n  async storeTokens(tokens) {\n    const encryptedTokens = this.encrypt(tokens);\n    await chrome.storage.local.set({\n      'linkedin_tokens': encryptedTokens,\n      'token_timestamp': Date.now()\n    });\n  }\n\n  // Store encrypted user profile\n  async storeUserProfile(profile) {\n    const encryptedProfile = this.encrypt(profile);\n    await chrome.storage.local.set({\n      'user_profile': encryptedProfile\n    });\n  }\n\n  // Get stored tokens\n  async getTokens() {\n    const result = await chrome.storage.local.get(['linkedin_tokens', 'token_timestamp']);\n    if (result.linkedin_tokens) {\n      const tokens = this.decrypt(result.linkedin_tokens);\n      if (tokens && this.isTokenValid(result.token_timestamp, tokens.expires_in)) {\n        return tokens;\n      }\n    }\n    return null;\n  }\n\n  // Get stored user profile\n  async getUserProfile() {\n    const result = await chrome.storage.local.get('user_profile');\n    if (result.user_profile) {\n      return this.decrypt(result.user_profile);\n    }\n    return null;\n  }\n\n  // Check if token is still valid\n  isTokenValid(timestamp, expiresIn) {\n    const now = Date.now();\n    const tokenAge = (now - timestamp) / 1000; // Convert to seconds\n    return tokenAge < (expiresIn - 300); // 5 minute buffer\n  }\n\n  // Check authentication status\n  async isAuthenticated() {\n    const tokens = await this.getTokens();\n    return tokens !== null;\n  }\n\n  // Logout and clear stored data\n  async logout() {\n    await chrome.storage.local.remove(['linkedin_tokens', 'token_timestamp', 'user_profile']);\n  }\n\n  // Refresh access token if needed\n  async refreshTokenIfNeeded() {\n    const tokens = await this.getTokens();\n    if (!tokens) return null;\n\n    const result = await chrome.storage.local.get('token_timestamp');\n    if (!this.isTokenValid(result.token_timestamp, tokens.expires_in)) {\n      // Token expired, need to re-authenticate\n      await this.logout();\n      return null;\n    }\n\n    return tokens;\n  }\n}\n\n// API Manager for job data submission\nclass ApiManager {\n  constructor() {\n    this.authManager = new AuthManager();\n    this.maxRetries = 3;\n    this.retryDelay = 1000; // 1 second\n    this.security = new SecurityManager();\n  }\n\n  async getApiConfig() {\n    // SaaS configuration - no user configuration needed\n    return {\n      endpoint: 'http://localhost:3002/api/jobs', // Development endpoint\n      apiKey: '' // No API key needed - authentication via LinkedIn token\n    };\n  }\n\n  // Check if portal is logged in\n  async checkPortalAuth() {\n    try {\n      const response = await fetch('http://localhost:3002/api/auth/verify', {\n        method: 'GET',\n        credentials: 'include', // Include cookies for JWT\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        return data.success ? data.user : null;\n      }\n      return null;\n    } catch (error) {\n      console.log('Portal not logged in or not accessible:', error.message);\n      return null;\n    }\n  }\n\n  async submitJob(jobData) {\n    // Validate and sanitize job data\n    const validation = this.security.validateJobData(jobData);\n    if (!validation.valid) {\n      this.security.logSecurityEvent('invalid_job_data', { errors: validation.errors });\n      throw new Error('Invalid job data: ' + validation.errors.join(', '));\n    }\n\n    const sanitizedJobData = this.security.sanitizeJobData(jobData);\n\n    const config = await this.getApiConfig();\n\n    // Check if portal is logged in first\n    const portalUser = await this.checkPortalAuth();\n    if (portalUser) {\n      console.log('Portal is logged in, using portal authentication');\n      return await this.submitJobViaPortal(sanitizedJobData, config);\n    }\n\n    // Fallback to extension LinkedIn authentication\n    console.log('Portal not logged in, using extension authentication');\n    return await this.submitJobViaExtension(sanitizedJobData, config);\n  }\n\n  async submitJobViaPortal(jobData, config) {\n    // Check rate limiting\n    const rateLimit = this.security.checkRateLimit('api_submissions', 10, 60000);\n    if (!rateLimit.allowed) {\n      this.security.logSecurityEvent('rate_limit_exceeded', { resetTime: rateLimit.resetTime });\n      throw new Error('Rate limit exceeded. Please try again later.');\n    }\n\n    // Add metadata to job data\n    const enrichedJobData = {\n      ...jobData,\n      submitted_at: new Date().toISOString(),\n      extension_version: chrome.runtime.getManifest().version,\n      user_agent: navigator.userAgent,\n      source: 'chrome_extension_via_portal'\n    };\n\n    return await this.submitWithRetryPortal(config, enrichedJobData);\n  }\n\n  async submitJobViaExtension(jobData, config) {\n    // Check rate limiting\n    const rateLimit = this.security.checkRateLimit('api_submissions', 10, 60000);\n    if (!rateLimit.allowed) {\n      this.security.logSecurityEvent('rate_limit_exceeded', { resetTime: rateLimit.resetTime });\n      throw new Error('Rate limit exceeded. Please try again later.');\n    }\n\n    const tokens = await this.authManager.getTokens();\n    if (!tokens) {\n      throw new Error('User not authenticated. Please login with LinkedIn or use the Job Tracker portal.');\n    }\n\n    // Add metadata to job data\n    const enrichedJobData = {\n      ...jobData,\n      submitted_at: new Date().toISOString(),\n      extension_version: chrome.runtime.getManifest().version,\n      user_agent: navigator.userAgent,\n      source: 'chrome_extension_direct'\n    };\n\n    return await this.submitWithRetry(config, enrichedJobData, tokens);\n  }\n\n  async submitWithRetryPortal(config, jobData, attempt = 1) {\n    try {\n      const response = await fetch(config.endpoint, {\n        method: 'POST',\n        credentials: 'include', // Include cookies for JWT authentication\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Extension-Version': chrome.runtime.getManifest().version,\n          'X-User-Agent': navigator.userAgent\n        },\n        body: JSON.stringify(jobData)\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        let errorMessage = `API request failed: ${response.status}`;\n\n        try {\n          const errorData = JSON.parse(errorText);\n          errorMessage = errorData.error || errorMessage;\n        } catch (e) {\n          // Use default error message if response is not JSON\n        }\n\n        // Don't retry on client errors (4xx)\n        if (response.status >= 400 && response.status < 500) {\n          throw new Error(errorMessage);\n        }\n\n        // Retry on server errors (5xx) or network errors\n        if (attempt < 3) {\n          console.log(`Portal submission attempt ${attempt} failed, retrying...`);\n          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\n          return this.submitWithRetryPortal(config, jobData, attempt + 1);\n        }\n\n        throw new Error(errorMessage);\n      }\n\n      const result = await response.json();\n      return {\n        success: true,\n        data: result,\n        message: 'Job saved successfully via portal authentication'\n      };\n\n    } catch (error) {\n      if (attempt < 3 && !error.message.includes('API request failed')) {\n        console.log(`Portal submission attempt ${attempt} failed, retrying...`);\n        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\n        return this.submitWithRetryPortal(config, jobData, attempt + 1);\n      }\n      throw error;\n    }\n  }\n\n  async submitWithRetry(config, jobData, tokens, attempt = 1) {\n    try {\n      const response = await fetch(config.endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${tokens.access_token}`, // Use LinkedIn token for auth\n          'X-Extension-Version': chrome.runtime.getManifest().version,\n          'X-User-Agent': navigator.userAgent\n        },\n        body: JSON.stringify(jobData)\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        let errorMessage = `API request failed: ${response.status}`;\n\n        try {\n          const errorData = JSON.parse(errorText);\n          errorMessage = errorData.message || errorMessage;\n        } catch (e) {\n          // Use default error message if response is not JSON\n        }\n\n        // Don't retry on client errors (4xx)\n        if (response.status >= 400 && response.status < 500) {\n          throw new Error(errorMessage);\n        }\n\n        // Retry on server errors (5xx) or network issues\n        if (attempt < this.maxRetries) {\n          await this.delay(this.retryDelay * attempt);\n          return await this.submitWithRetry(config, jobData, tokens, attempt + 1);\n        }\n\n        throw new Error(`${errorMessage} (after ${this.maxRetries} attempts)`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error.name === 'TypeError' && attempt < this.maxRetries) {\n        // Network error, retry\n        await this.delay(this.retryDelay * attempt);\n        return await this.submitWithRetry(config, jobData, tokens, attempt + 1);\n      }\n      throw error;\n    }\n  }\n\n  async validateApiEndpoint(endpoint) {\n    // First validate with security manager\n    const securityValidation = this.security.validateApiEndpoint(endpoint);\n    if (!securityValidation.valid) {\n      return false;\n    }\n\n    try {\n      const response = await fetch(endpoint, {\n        method: 'OPTIONS',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      return response.ok || response.status === 405; // 405 Method Not Allowed is acceptable for OPTIONS\n    } catch (error) {\n      this.security.logSecurityEvent('api_endpoint_validation_failed', { endpoint, error: error.message });\n      return false;\n    }\n  }\n\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  async getSubmissionHistory() {\n    const result = await chrome.storage.local.get('submission_history');\n    return result.submission_history || [];\n  }\n\n  async addToSubmissionHistory(jobData, result) {\n    const history = await this.getSubmissionHistory();\n    const entry = {\n      job_id: jobData.job_id,\n      job_title: jobData.job_title,\n      company_name: jobData.company.name,\n      submitted_at: new Date().toISOString(),\n      success: !!result.success,\n      error: result.error || null\n    };\n\n    history.unshift(entry);\n\n    // Keep only last 50 submissions\n    if (history.length > 50) {\n      history.splice(50);\n    }\n\n    await chrome.storage.local.set({ submission_history: history });\n  }\n}\n\n// Initialize managers\nconst authManager = new AuthManager();\nconst apiManager = new ApiManager();\n\n// Message handling\nchrome.runtime.onMessage.addListener((request, sender, sendResponse) => {\n  (async () => {\n    try {\n      switch (request.action) {\n        case 'authenticate':\n          const authResult = await authManager.authenticate();\n          sendResponse(authResult);\n          break;\n\n        case 'checkAuth':\n          const isAuth = await authManager.isAuthenticated();\n          const profile = isAuth ? await authManager.getUserProfile() : null;\n          sendResponse({ authenticated: isAuth, profile });\n          break;\n\n        case 'checkPortalAuth':\n          try {\n            const portalUser = await apiManager.checkPortalAuth();\n            sendResponse({\n              success: !!portalUser,\n              user: portalUser,\n              connected: !!portalUser\n            });\n          } catch (error) {\n            sendResponse({\n              success: false,\n              user: null,\n              connected: false,\n              error: error.message\n            });\n          }\n          break;\n\n        case 'logout':\n          await authManager.logout();\n          sendResponse({ success: true });\n          break;\n\n        case 'submitJob':\n          const result = await apiManager.submitJob(request.jobData);\n          await apiManager.addToSubmissionHistory(request.jobData, { success: true, result });\n          sendResponse({ success: true, result });\n          break;\n\n        case 'validateApiEndpoint':\n          const isValid = await apiManager.validateApiEndpoint(request.endpoint);\n          sendResponse({ valid: isValid });\n          break;\n\n        case 'getSubmissionHistory':\n          const history = await apiManager.getSubmissionHistory();\n          sendResponse({ history });\n          break;\n\n        case 'refreshToken':\n          const tokens = await authManager.refreshTokenIfNeeded();\n          sendResponse({ tokens });\n          break;\n\n        default:\n          sendResponse({ error: 'Unknown action' });\n      }\n    } catch (error) {\n      // Log submission errors to history\n      if (request.action === 'submitJob') {\n        await apiManager.addToSubmissionHistory(request.jobData, { success: false, error: error.message });\n      }\n      sendResponse({ error: error.message });\n    }\n  })();\n  \n  return true; // Keep message channel open for async response\n});\n\n// Extension installation/update handler\nchrome.runtime.onInstalled.addListener((details) => {\n  if (details.reason === 'install') {\n    chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });\n  }\n});\n"], "names": [], "sourceRoot": ""}