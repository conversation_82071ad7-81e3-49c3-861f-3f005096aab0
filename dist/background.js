/******/ (() => { // webpackBootstrap
/*!***************************!*\
  !*** ./src/background.js ***!
  \***************************/
// Background Service Worker for Job Tracker Extension

// Import security manager
importScripts('security.js');

// Simple encryption/decryption using built-in crypto
const CryptoJS = {
  AES: {
    encrypt: (data, key) => {
      // Simple base64 encoding for demo - in production use proper encryption
      return btoa(JSON.stringify({ data, key: key.substring(0, 8) }));
    },
    decrypt: (encryptedData, key) => {
      try {
        const decoded = JSON.parse(atob(encryptedData));
        if (decoded.key === key.substring(0, 8)) {
          return { toString: () => decoded.data };
        }
        return null;
      } catch (error) {
        return null;
      }
    }
  },
  enc: {
    Utf8: {}
  }
};

class AuthManager {
  constructor() {
    // LinkedIn Social Login - no client ID needed for public social login
    this.clientId = '772ojq74j5zy0p'; // LinkedIn's public social login client ID
    this.redirectUri = chrome.identity.getRedirectURL();
    this.scope = 'r_liteprofile r_emailaddress';
    this.encryptionKey = 'job-tracker-secret-key'; // In production, generate this dynamically
    this.security = new SecurityManager();

    // LinkedIn OAuth endpoints
    this.authUrl = 'https://www.linkedin.com/oauth/v2/authorization';
    this.tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    this.profileUrl = 'https://api.linkedin.com/v2/people/~';
  }

  // Encrypt sensitive data before storage
  encrypt(data) {
    return CryptoJS.AES.encrypt(JSON.stringify(data), this.encryptionKey).toString();
  }

  // Decrypt sensitive data after retrieval
  decrypt(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
      console.error('Decryption failed:', error);
      return null;
    }
  }

  // Initiate LinkedIn Social Login flow
  async authenticate() {
    const state = this.generateState();
    const authUrl = `${this.authUrl}?` +
      `response_type=code&` +
      `client_id=${this.clientId}&` +
      `redirect_uri=${encodeURIComponent(this.redirectUri)}&` +
      `scope=${encodeURIComponent(this.scope)}&` +
      `state=${state}`;

    try {
      const responseUrl = await chrome.identity.launchWebAuthFlow({
        url: authUrl,
        interactive: true
      });

      const urlParams = new URLSearchParams(new URL(responseUrl).search);
      const code = urlParams.get('code');
      const returnedState = urlParams.get('state');

      // Validate state parameter for security
      if (!this.security.validateState(returnedState, state)) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      if (code) {
        const tokens = await this.exchangeCodeForTokens(code);
        await this.storeTokens(tokens);
        const profile = await this.fetchUserProfile(tokens.access_token);
        await this.storeUserProfile(profile);
        return { success: true, profile };
      } else {
        throw new Error('No authorization code received');
      }
    } catch (error) {
      console.error('LinkedIn Social Login failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Generate random state for OAuth security
  generateState() {
    return this.security.generateSecureState();
  }

  // Extract authorization code from redirect URL
  extractCodeFromUrl(url) {
    const urlParams = new URLSearchParams(new URL(url).search);
    return urlParams.get('code');
  }

  // Exchange authorization code for access tokens using LinkedIn Social Login
  async exchangeCodeForTokens(code) {
    // For LinkedIn Social Login, we use a simplified token exchange
    // that doesn't require a client secret for public social login
    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: this.redirectUri,
      client_id: this.clientId
      // No client_secret needed for LinkedIn Social Login
    });

    const response = await fetch(this.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: params
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token exchange failed:', errorText);
      throw new Error(`Token exchange failed: ${response.status}`);
    }

    const tokens = await response.json();

    // Validate token response
    if (!tokens.access_token) {
      throw new Error('No access token received from LinkedIn');
    }

    return tokens;
  }

  // Fetch user profile from LinkedIn API
  async fetchUserProfile(accessToken) {
    try {
      // Fetch basic profile information
      const profileResponse = await fetch(this.profileUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!profileResponse.ok) {
        throw new Error(`Profile fetch failed: ${profileResponse.status}`);
      }

      const profile = await profileResponse.json();

      // Fetch email address separately (different endpoint)
      const emailResponse = await fetch('https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      let email = null;
      if (emailResponse.ok) {
        const emailData = await emailResponse.json();
        if (emailData.elements && emailData.elements.length > 0) {
          email = emailData.elements[0]['handle~'].emailAddress;
        }
      }

      // Combine profile and email data
      return {
        ...profile,
        email: email
      };
    } catch (error) {
      console.error('Failed to fetch LinkedIn profile:', error);
      throw new Error('Failed to fetch user profile from LinkedIn');
    }
  }

  // Store encrypted tokens
  async storeTokens(tokens) {
    const encryptedTokens = this.encrypt(tokens);
    await chrome.storage.local.set({
      'linkedin_tokens': encryptedTokens,
      'token_timestamp': Date.now()
    });
  }

  // Store encrypted user profile
  async storeUserProfile(profile) {
    const encryptedProfile = this.encrypt(profile);
    await chrome.storage.local.set({
      'user_profile': encryptedProfile
    });
  }

  // Get stored tokens
  async getTokens() {
    const result = await chrome.storage.local.get(['linkedin_tokens', 'token_timestamp']);
    if (result.linkedin_tokens) {
      const tokens = this.decrypt(result.linkedin_tokens);
      if (tokens && this.isTokenValid(result.token_timestamp, tokens.expires_in)) {
        return tokens;
      }
    }
    return null;
  }

  // Get stored user profile
  async getUserProfile() {
    const result = await chrome.storage.local.get('user_profile');
    if (result.user_profile) {
      return this.decrypt(result.user_profile);
    }
    return null;
  }

  // Check if token is still valid
  isTokenValid(timestamp, expiresIn) {
    const now = Date.now();
    const tokenAge = (now - timestamp) / 1000; // Convert to seconds
    return tokenAge < (expiresIn - 300); // 5 minute buffer
  }

  // Check authentication status
  async isAuthenticated() {
    const tokens = await this.getTokens();
    return tokens !== null;
  }

  // Logout and clear stored data
  async logout() {
    await chrome.storage.local.remove(['linkedin_tokens', 'token_timestamp', 'user_profile']);
  }

  // Refresh access token if needed
  async refreshTokenIfNeeded() {
    const tokens = await this.getTokens();
    if (!tokens) return null;

    const result = await chrome.storage.local.get('token_timestamp');
    if (!this.isTokenValid(result.token_timestamp, tokens.expires_in)) {
      // Token expired, need to re-authenticate
      await this.logout();
      return null;
    }

    return tokens;
  }
}

// API Manager for job data submission
class ApiManager {
  constructor() {
    this.authManager = new AuthManager();
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
    this.security = new SecurityManager();
  }

  async getApiConfig() {
    // SaaS configuration - no user configuration needed
    return {
      endpoint: 'http://localhost:3002/api/jobs', // Development endpoint
      apiKey: '' // No API key needed - authentication via LinkedIn token
    };
  }

  // Check if portal is logged in
  async checkPortalAuth() {
    try {
      const response = await fetch('http://localhost:3002/api/auth/verify', {
        method: 'GET',
        credentials: 'include', // Include cookies for JWT
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.success ? data.user : null;
      }
      return null;
    } catch (error) {
      console.log('Portal not logged in or not accessible:', error.message);
      return null;
    }
  }

  async submitJob(jobData) {
    // Validate and sanitize job data
    const validation = this.security.validateJobData(jobData);
    if (!validation.valid) {
      this.security.logSecurityEvent('invalid_job_data', { errors: validation.errors });
      throw new Error('Invalid job data: ' + validation.errors.join(', '));
    }

    const sanitizedJobData = this.security.sanitizeJobData(jobData);

    const config = await this.getApiConfig();

    // Check if portal is logged in first
    const portalUser = await this.checkPortalAuth();
    if (portalUser) {
      console.log('Portal is logged in, using portal authentication');
      return await this.submitJobViaPortal(sanitizedJobData, config);
    }

    // Fallback to extension LinkedIn authentication
    console.log('Portal not logged in, using extension authentication');
    return await this.submitJobViaExtension(sanitizedJobData, config);
  }

  async submitJobViaPortal(jobData, config) {
    // Check rate limiting
    const rateLimit = this.security.checkRateLimit('api_submissions', 10, 60000);
    if (!rateLimit.allowed) {
      this.security.logSecurityEvent('rate_limit_exceeded', { resetTime: rateLimit.resetTime });
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    // Add metadata to job data
    const enrichedJobData = {
      ...jobData,
      submitted_at: new Date().toISOString(),
      extension_version: chrome.runtime.getManifest().version,
      user_agent: navigator.userAgent,
      source: 'chrome_extension_via_portal'
    };

    return await this.submitWithRetryPortal(config, enrichedJobData);
  }

  async submitJobViaExtension(jobData, config) {
    // Check rate limiting
    const rateLimit = this.security.checkRateLimit('api_submissions', 10, 60000);
    if (!rateLimit.allowed) {
      this.security.logSecurityEvent('rate_limit_exceeded', { resetTime: rateLimit.resetTime });
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    const tokens = await this.authManager.getTokens();
    if (!tokens) {
      throw new Error('User not authenticated. Please login with LinkedIn or use the Job Tracker portal.');
    }

    // Add metadata to job data
    const enrichedJobData = {
      ...jobData,
      submitted_at: new Date().toISOString(),
      extension_version: chrome.runtime.getManifest().version,
      user_agent: navigator.userAgent,
      source: 'chrome_extension_direct'
    };

    return await this.submitWithRetry(config, enrichedJobData, tokens);
  }

  async submitWithRetryPortal(config, jobData, attempt = 1) {
    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        credentials: 'include', // Include cookies for JWT authentication
        headers: {
          'Content-Type': 'application/json',
          'X-Extension-Version': chrome.runtime.getManifest().version,
          'X-User-Agent': navigator.userAgent
        },
        body: JSON.stringify(jobData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `API request failed: ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // Use default error message if response is not JSON
        }

        // Don't retry on client errors (4xx)
        if (response.status >= 400 && response.status < 500) {
          throw new Error(errorMessage);
        }

        // Retry on server errors (5xx) or network errors
        if (attempt < 3) {
          console.log(`Portal submission attempt ${attempt} failed, retrying...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          return this.submitWithRetryPortal(config, jobData, attempt + 1);
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();
      return {
        success: true,
        data: result,
        message: 'Job saved successfully via portal authentication'
      };

    } catch (error) {
      if (attempt < 3 && !error.message.includes('API request failed')) {
        console.log(`Portal submission attempt ${attempt} failed, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        return this.submitWithRetryPortal(config, jobData, attempt + 1);
      }
      throw error;
    }
  }

  async submitWithRetry(config, jobData, tokens, attempt = 1) {
    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokens.access_token}`, // Use LinkedIn token for auth
          'X-Extension-Version': chrome.runtime.getManifest().version,
          'X-User-Agent': navigator.userAgent
        },
        body: JSON.stringify(jobData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `API request failed: ${response.status}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // Use default error message if response is not JSON
        }

        // Don't retry on client errors (4xx)
        if (response.status >= 400 && response.status < 500) {
          throw new Error(errorMessage);
        }

        // Retry on server errors (5xx) or network issues
        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
          return await this.submitWithRetry(config, jobData, tokens, attempt + 1);
        }

        throw new Error(`${errorMessage} (after ${this.maxRetries} attempts)`);
      }

      return await response.json();
    } catch (error) {
      if (error.name === 'TypeError' && attempt < this.maxRetries) {
        // Network error, retry
        await this.delay(this.retryDelay * attempt);
        return await this.submitWithRetry(config, jobData, tokens, attempt + 1);
      }
      throw error;
    }
  }

  async validateApiEndpoint(endpoint) {
    // First validate with security manager
    const securityValidation = this.security.validateApiEndpoint(endpoint);
    if (!securityValidation.valid) {
      return false;
    }

    try {
      const response = await fetch(endpoint, {
        method: 'OPTIONS',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.ok || response.status === 405; // 405 Method Not Allowed is acceptable for OPTIONS
    } catch (error) {
      this.security.logSecurityEvent('api_endpoint_validation_failed', { endpoint, error: error.message });
      return false;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getSubmissionHistory() {
    const result = await chrome.storage.local.get('submission_history');
    return result.submission_history || [];
  }

  async addToSubmissionHistory(jobData, result) {
    const history = await this.getSubmissionHistory();
    const entry = {
      job_id: jobData.job_id,
      job_title: jobData.job_title,
      company_name: jobData.company.name,
      submitted_at: new Date().toISOString(),
      success: !!result.success,
      error: result.error || null
    };

    history.unshift(entry);

    // Keep only last 50 submissions
    if (history.length > 50) {
      history.splice(50);
    }

    await chrome.storage.local.set({ submission_history: history });
  }
}

// Initialize managers
const authManager = new AuthManager();
const apiManager = new ApiManager();

// Message handling
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  (async () => {
    try {
      switch (request.action) {
        case 'authenticate':
          const authResult = await authManager.authenticate();
          sendResponse(authResult);
          break;

        case 'checkAuth':
          const isAuth = await authManager.isAuthenticated();
          const profile = isAuth ? await authManager.getUserProfile() : null;
          sendResponse({ authenticated: isAuth, profile });
          break;

        case 'checkPortalAuth':
          try {
            const portalUser = await apiManager.checkPortalAuth();
            sendResponse({
              success: !!portalUser,
              user: portalUser,
              connected: !!portalUser
            });
          } catch (error) {
            sendResponse({
              success: false,
              user: null,
              connected: false,
              error: error.message
            });
          }
          break;

        case 'logout':
          await authManager.logout();
          sendResponse({ success: true });
          break;

        case 'submitJob':
          const result = await apiManager.submitJob(request.jobData);
          await apiManager.addToSubmissionHistory(request.jobData, { success: true, result });
          sendResponse({ success: true, result });
          break;

        case 'validateApiEndpoint':
          const isValid = await apiManager.validateApiEndpoint(request.endpoint);
          sendResponse({ valid: isValid });
          break;

        case 'getSubmissionHistory':
          const history = await apiManager.getSubmissionHistory();
          sendResponse({ history });
          break;

        case 'refreshToken':
          const tokens = await authManager.refreshTokenIfNeeded();
          sendResponse({ tokens });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      // Log submission errors to history
      if (request.action === 'submitJob') {
        await apiManager.addToSubmissionHistory(request.jobData, { success: false, error: error.message });
      }
      sendResponse({ error: error.message });
    }
  })();
  
  return true; // Keep message channel open for async response
});

// Extension installation/update handler
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
  }
});

/******/ })()
;
//# sourceMappingURL=background.js.map