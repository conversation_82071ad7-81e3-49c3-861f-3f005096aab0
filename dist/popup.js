(()=>{class e{constructor(){this.currentJobData=null,this.isAuthenticated=!1,this.userProfile=null,this.init()}async init(){this.bindEvents(),await this.checkAuthenticationStatus()}bindEvents(){document.getElementById("login-btn").addEventListener("click",()=>this.handleLogin()),document.getElementById("logout-btn").addEventListener("click",()=>this.handleLogout()),document.getElementById("add-job-btn").addEventListener("click",()=>this.handleAddJob()),document.getElementById("description-toggle").addEventListener("click",()=>this.toggleDescription()),document.getElementById("retry-btn").addEventListener("click",()=>this.init())}async checkAuthenticationStatus(){try{this.showLoading();const[e,t]=await Promise.all([this.sendMessage({action:"checkAuth"}),this.sendMessage({action:"checkPortalAuth"})]);if(e.error&&!t.success)return void this.showError(e.error);this.isAuthenticated=e.authenticated||t.success,this.userProfile=e.profile||t.user,this.portalConnected=t.success,this.isAuthenticated?(this.showMainContent(),await this.loadJobData()):this.showAuthRequired()}catch(e){this.showError("Failed to check authentication status")}}async handleLogin(){try{this.showLoading("Authenticating with LinkedIn...");const e=await this.sendMessage({action:"authenticate"});e.success?(this.isAuthenticated=!0,this.userProfile=e.profile,this.showMainContent(),await this.loadJobData(),this.showStatusMessage("Successfully authenticated!","success")):this.showError(e.error||"Authentication failed")}catch(e){this.showError("Authentication failed: "+e.message)}}async handleLogout(){try{await this.sendMessage({action:"logout"}),this.isAuthenticated=!1,this.userProfile=null,this.currentJobData=null,this.showAuthRequired(),this.showStatusMessage("Successfully logged out","info")}catch(e){this.showError("Logout failed: "+e.message)}}async loadJobData(){try{const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});if(!e.url.includes("linkedin.com/jobs/view/"))return void this.showNoJob();const t=await chrome.tabs.sendMessage(e.id,{action:"extractJobData"});t&&t.jobData?(this.currentJobData=t.jobData,this.displayJobData(this.currentJobData)):this.showNoJob()}catch(e){console.error("Failed to load job data:",e),this.showNoJob()}}async handleAddJob(){if(this.currentJobData)try{const e=document.getElementById("add-job-btn");e.disabled=!0,e.textContent="Saving...";const t=await this.sendMessage({action:"submitJob",jobData:this.currentJobData});if(!t.success)throw new Error(t.error||"Failed to save job");this.showStatusMessage("Job successfully saved to Job Tracker!","success"),e.innerHTML='\n          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">\n            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>\n          </svg>\n          Saved ✓\n        ',setTimeout(()=>{e.innerHTML='\n            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">\n              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>\n            </svg>\n            Save to Job Tracker\n          ',e.disabled=!1},3e3)}catch(e){this.showStatusMessage("Failed to save job: "+e.message,"error");const t=document.getElementById("add-job-btn");t.innerHTML='\n        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">\n          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>\n        </svg>\n        Save to Job Tracker\n      ',t.disabled=!1}else this.showStatusMessage("No job data available","error")}displayJobData(e){this.userProfile&&(document.getElementById("user-name").textContent=this.userProfile.localizedFirstName+" "+this.userProfile.localizedLastName,document.getElementById("user-avatar").src="icons/icon32.png"),document.getElementById("no-job").style.display="none",document.getElementById("job-details").style.display="block",document.getElementById("job-title").textContent=e.job_title||"Unknown Title",document.getElementById("company-name").textContent=e.company.name||"Unknown Company",document.getElementById("company-name").href=e.company.linkedin_url||"#",e.company.logo_url?document.getElementById("company-logo").src=e.company.logo_url:document.getElementById("company-logo").src="icons/icon32.png",document.getElementById("job-location").textContent=e.location||"Not specified",document.getElementById("workplace-type").textContent=e.workplace_type||"Not specified",document.getElementById("employment-type").textContent=e.employment_type||"Not specified",document.getElementById("posted-time").textContent=e.posted_time||"Not specified",document.getElementById("applicants").textContent=e.applicants||"Not specified",document.getElementById("apply-link").href=e.apply_link||"#",e.easy_apply&&(document.getElementById("easy-apply").style.display="inline-block"),e.job_description&&(document.getElementById("job-summary").textContent=e.job_description.summary||"No summary available",this.populateList("job-responsibilities",e.job_description.responsibilities),this.populateList("job-requirements",e.job_description.requirements),this.populateList("job-benefits",e.job_description.benefits))}populateList(e,t){const n=document.getElementById(e);if(n.innerHTML="",t&&t.length>0)t.forEach(e=>{const t=document.createElement("li");t.textContent=e,n.appendChild(t)});else{const e=document.createElement("li");e.textContent="Not specified",e.style.fontStyle="italic",e.style.color="#999",n.appendChild(e)}}toggleDescription(){const e=document.getElementById("description-content"),t=document.getElementById("description-toggle").querySelector(".toggle-icon");"none"===e.style.display?(e.style.display="block",t.classList.add("rotated")):(e.style.display="none",t.classList.remove("rotated"))}showLoading(e="Loading..."){this.hideAllStates(),document.getElementById("loading").style.display="flex",document.querySelector("#loading p").textContent=e}showAuthRequired(){this.hideAllStates(),document.getElementById("auth-required").style.display="block"}showMainContent(){if(this.hideAllStates(),document.getElementById("main-content").style.display="block",this.userProfile){const e=`${this.userProfile.localizedFirstName||this.userProfile.first_name||""} ${this.userProfile.localizedLastName||this.userProfile.last_name||""}`.trim(),t=document.getElementById("user-name");t&&(t.textContent=e||"LinkedIn User");const n=document.getElementById("user-avatar");n&&(this.userProfile.profilePicture||this.userProfile.profile_picture)&&(n.src=this.userProfile.profilePicture||this.userProfile.profile_picture)}this.updatePortalStatus()}updatePortalStatus(){const e=document.getElementById("portal-status");e&&(this.portalConnected?(e.innerHTML='\n          <div class="status-indicator success"></div>\n          <span>Portal Connected</span>\n        ',e.className="portal-status connected"):(e.innerHTML='\n          <div class="status-indicator warning"></div>\n          <span>Portal Disconnected</span>\n        ',e.className="portal-status disconnected"))}showError(e){this.hideAllStates(),document.getElementById("error-state").style.display="block",document.getElementById("error-message").textContent=e}showNoJob(){document.getElementById("no-job").style.display="block",document.getElementById("job-details").style.display="none"}hideAllStates(){document.getElementById("loading").style.display="none",document.getElementById("auth-required").style.display="none",document.getElementById("main-content").style.display="none",document.getElementById("error-state").style.display="none"}showStatusMessage(e,t="info"){const n=document.getElementById("status-messages"),s=document.createElement("div");s.className=`status-message status-${t}`,s.textContent=e,n.appendChild(s),setTimeout(()=>{s.parentNode&&s.parentNode.removeChild(s)},5e3)}async sendMessage(e){return new Promise(t=>{chrome.runtime.sendMessage(e,t)})}}document.addEventListener("DOMContentLoaded",()=>{new e})})();