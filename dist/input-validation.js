class InputValidator{constructor(){this.patterns={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,url:/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,jobId:/^\d+$/,alphanumeric:/^[a-zA-Z0-9\s]+$/,safeText:/^[a-zA-Z0-9\s\-_.,!?()]+$/}}validateEmail(e){return"string"==typeof e&&this.patterns.email.test(e.trim())}validateUrl(e){if("string"!=typeof e)return!1;try{const t=new URL(e);return!!["https:","http:"].includes(t.protocol)&&!("localhost"===t.hostname&&!this.isDevelopment())&&!this.isPrivateIP(t.hostname)&&this.patterns.url.test(e)}catch(e){return!1}}isDevelopment(){return chrome.runtime.getManifest().version.includes("dev")||"0.0.1"===chrome.runtime.getManifest().version}isPrivateIP(e){return[/^10\./,/^172\.(1[6-9]|2[0-9]|3[0-1])\./,/^192\.168\./,/^127\./,/^169\.254\./,/^::1$/,/^fc00:/,/^fe80:/].some(t=>t.test(e))}validateJobId(e){if(!e)return!1;const t=e.toString().trim();return this.patterns.jobId.test(t)&&t.length<=20}validateSafeText(e,t=1e3){return"string"==typeof e&&(!(e.length>t)&&![/<script/i,/javascript:/i,/vbscript:/i,/on\w+\s*=/i,/<iframe/i,/<object/i,/<embed/i,/<link/i,/<meta/i,/data:text\/html/i].some(t=>t.test(e)))}validateApiKey(e){return"string"==typeof e&&!(e.length<16||e.length>128)&&/^[a-zA-Z0-9\-_\.]+$/.test(e)}validateWorkplaceType(e){return["Remote","Hybrid","Onsite","On-site"].includes(e)}validateEmploymentType(e){return["Full-time","Part-time","Contract","Temporary","Internship","Volunteer"].includes(e)}validateStringArray(e,t=50,i=500){return!!Array.isArray(e)&&!(e.length>t)&&e.every(e=>"string"==typeof e&&e.length<=i&&this.validateSafeText(e,i))}validatePersonObject(e){return!(!e||"object"!=typeof e)&&[!e.name||this.validateSafeText(e.name,100),!e.linkedin_url||this.validateUrl(e.linkedin_url)&&e.linkedin_url.includes("linkedin.com"),!e.title||this.validateSafeText(e.title,200),!e.connection_degree||/^(1st|2nd|3rd|\d+th)$/.test(e.connection_degree),!e.profile_image||this.validateUrl(e.profile_image)].every(e=>!0===e)}validateJobDataStructure(e){const t=[];if(this.validateJobId(e.job_id)||t.push("Invalid job_id"),this.validateSafeText(e.job_title,200)||t.push("Invalid job_title"),e.company&&"object"==typeof e.company?(this.validateSafeText(e.company.name,100)||t.push("Invalid company name"),e.company.linkedin_url&&!this.validateUrl(e.company.linkedin_url)&&t.push("Invalid company LinkedIn URL"),e.company.logo_url&&!this.validateUrl(e.company.logo_url)&&t.push("Invalid company logo URL")):t.push("Missing or invalid company object"),e.location&&!this.validateSafeText(e.location,100)&&t.push("Invalid location"),e.workplace_type&&!this.validateWorkplaceType(e.workplace_type)&&t.push("Invalid workplace_type"),e.employment_type&&!this.validateEmploymentType(e.employment_type)&&t.push("Invalid employment_type"),e.apply_link&&!this.validateUrl(e.apply_link)&&t.push("Invalid apply_link"),e.job_description&&"object"==typeof e.job_description){const i=e.job_description;i.summary&&!this.validateSafeText(i.summary,1e3)&&t.push("Invalid job description summary"),i.responsibilities&&!this.validateStringArray(i.responsibilities,20,500)&&t.push("Invalid responsibilities array"),i.requirements&&!this.validateStringArray(i.requirements,20,500)&&t.push("Invalid requirements array"),i.benefits&&!this.validateStringArray(i.benefits,20,200)&&t.push("Invalid benefits array")}return e.people_you_can_reach&&(Array.isArray(e.people_you_can_reach)?e.people_you_can_reach.length>10?t.push("Too many people connections (max: 10)"):e.people_you_can_reach.forEach((e,i)=>{this.validatePersonObject(e)||t.push(`Invalid person object at index ${i}`)}):t.push("people_you_can_reach must be an array")),{valid:0===t.length,errors:t}}sanitizeInput(e,t=1e3){return"string"!=typeof e?"":e.trim().substring(0,t).replace(/[<>'"&]/g,"").replace(/\s+/g," ")}validateConfig(e){const t=[];return e.api_endpoint&&(this.validateUrl(e.api_endpoint)||t.push("Invalid API endpoint URL")),e.api_key&&(this.validateApiKey(e.api_key)||t.push("Invalid API key format")),{valid:0===t.length,errors:t}}}"undefined"!=typeof module&&module.exports?module.exports=InputValidator:"undefined"!=typeof window&&(window.InputValidator=InputValidator);