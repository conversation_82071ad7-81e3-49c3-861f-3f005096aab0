class SecurityManager{constructor(){this.allowedDomains=["linkedin.com","www.linkedin.com","api.linkedin.com"],this.maxDataSize=1048576}validateUrl(e){try{const t=new URL(e);return!!["https:","http:"].includes(t.protocol)&&(!e.includes("linkedin.com")||this.allowedDomains.some(e=>t.hostname===e||t.hostname.endsWith("."+e)))}catch(e){return!1}}sanitizeHtml(e){return"string"!=typeof e?"":e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/on\w+\s*=\s*"[^"]*"/gi,"").replace(/on\w+\s*=\s*'[^']*'/gi,"").replace(/javascript:/gi,"").replace(/vbscript:/gi,"").replace(/data:/gi,"").trim()}validateJobData(e){const t=[],i=JSON.stringify(e).length;i>this.maxDataSize&&t.push(`Job data too large: ${i} bytes (max: ${this.maxDataSize})`);const n=["job_id","job_title","company"];for(const i of n)e[i]||t.push(`Missing required field: ${i}`);e.job_id&&!/^\d+$/.test(e.job_id.toString())&&t.push("Invalid job_id format"),e.apply_link&&!this.validateUrl(e.apply_link)&&t.push("Invalid apply_link URL"),e.company?.linkedin_url&&!this.validateUrl(e.company.linkedin_url)&&t.push("Invalid company LinkedIn URL"),e.company?.logo_url&&!this.validateUrl(e.company.logo_url)&&t.push("Invalid company logo URL");const o={job_title:200,"company.name":100,location:100,workplace_type:50,employment_type:50,posted_time:100,applicants:100};for(const[i,n]of Object.entries(o)){const o=this.getNestedValue(e,i);o&&"string"==typeof o&&o.length>n&&t.push(`Field ${i} too long: ${o.length} chars (max: ${n})`)}return e.job_description?.responsibilities&&Array.isArray(e.job_description.responsibilities)&&e.job_description.responsibilities.length>20&&t.push("Too many responsibilities (max: 20)"),e.job_description?.requirements&&Array.isArray(e.job_description.requirements)&&e.job_description.requirements.length>20&&t.push("Too many requirements (max: 20)"),e.people_you_can_reach&&Array.isArray(e.people_you_can_reach)&&e.people_you_can_reach.length>10&&t.push("Too many people connections (max: 10)"),{valid:0===t.length,errors:t}}getNestedValue(e,t){return t.split(".").reduce((e,t)=>e?.[t],e)}sanitizeJobData(e){const t=JSON.parse(JSON.stringify(e)),i=e=>"string"!=typeof e?e:this.sanitizeHtml(e).substring(0,1e3);return t.job_title&&(t.job_title=i(t.job_title)),t.location&&(t.location=i(t.location)),t.workplace_type&&(t.workplace_type=i(t.workplace_type)),t.employment_type&&(t.employment_type=i(t.employment_type)),t.posted_time&&(t.posted_time=i(t.posted_time)),t.applicants&&(t.applicants=i(t.applicants)),t.company&&t.company.name&&(t.company.name=i(t.company.name)),t.job_description&&(t.job_description.summary&&(t.job_description.summary=i(t.job_description.summary)),["responsibilities","requirements","benefits"].forEach(e=>{Array.isArray(t.job_description[e])&&(t.job_description[e]=t.job_description[e].map(e=>i(e)).filter(e=>e.length>0).slice(0,20))})),Array.isArray(t.people_you_can_reach)&&(t.people_you_can_reach=t.people_you_can_reach.map(e=>({name:i(e.name||""),linkedin_url:this.validateUrl(e.linkedin_url)?e.linkedin_url:"",connection_degree:i(e.connection_degree||""),title:i(e.title||""),company_alum:i(e.company_alum||""),profile_image:this.validateUrl(e.profile_image)?e.profile_image:""})).slice(0,10)),t}generateSecureState(){const e=new Uint8Array(32);return crypto.getRandomValues(e),Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}validateState(e,t){if(!e||!t)return!1;if(e.length!==t.length)return!1;let i=0;for(let n=0;n<e.length;n++)i|=e.charCodeAt(n)^t.charCodeAt(n);return 0===i}checkRateLimit(e,t=10,i=6e4){const n=Date.now(),o=n-i,r=(this.getStoredRequests(e)||[]).filter(e=>e>o);return r.length>=t?{allowed:!1,resetTime:Math.min(...r)+i}:(r.push(n),this.storeRequests(e,r),{allowed:!0,remaining:t-r.length})}getStoredRequests(e){return[]}storeRequests(e,t){}validateApiEndpoint(e){if(!this.validateUrl(e))return{valid:!1,error:"Invalid URL format"};const t=new URL(e);return"https:"===t.protocol||t.hostname.includes("localhost")?["malicious-site.com","phishing-site.net"].some(e=>t.hostname.includes(e))?{valid:!1,error:"Blocked domain"}:{valid:!0}:{valid:!1,error:"HTTPS required for production endpoints"}}logSecurityEvent(e,t={}){const i={timestamp:(new Date).toISOString(),event:e,details:t,userAgent:navigator.userAgent,url:window.location?.href||"extension"};console.warn("Security Event:",i)}}"undefined"!=typeof module&&module.exports?module.exports=SecurityManager:"undefined"!=typeof window&&(window.SecurityManager=SecurityManager);