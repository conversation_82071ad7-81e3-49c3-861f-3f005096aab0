<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Job Tracker</title><link rel="stylesheet" href="styles/popup.css"><script defer="defer" src="popup.js"></script></head><body><div id="app"><div id="loading" class="loading-container"><div class="spinner"></div><p>Loading...</p></div><div id="auth-required" class="auth-container" style="display: none;"><div class="auth-header"><img src="icons/icon48.png" alt="Job Tracker" class="app-icon"><h2>Job Tracker</h2><p>Sign in with your LinkedIn account to start saving jobs</p></div><button id="login-btn" class="btn btn-primary"><svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg> Continue with LinkedIn</button> <small class="auth-note">Uses LinkedIn Social Login - no app installation required</small></div><div id="main-content" class="main-container" style="display: none;"><div class="user-header"><div class="user-info"><img id="user-avatar" src="" alt="User" class="user-avatar"><div class="user-details"><span id="user-name">Loading...</span> <button id="logout-btn" class="btn-link">Logout</button></div></div></div><div id="job-section"><div id="no-job" class="no-job-container"><div class="no-job-icon">📋</div><h3>No Job Detected</h3><p>Navigate to a LinkedIn job listing to extract job details.</p><small>Jobs are automatically saved to your Job Tracker account</small></div><div id="job-details" class="job-container" style="display: none;"><div class="job-header"><img id="company-logo" src="" alt="Company" class="company-logo"><div class="job-title-section"><h3 id="job-title">Job Title</h3><a id="company-name" href="#" target="_blank" class="company-link">Company Name</a></div></div><div class="job-meta"><div class="meta-item"><span class="meta-label">Location:</span> <span id="job-location">Location</span></div><div class="meta-item"><span class="meta-label">Type:</span> <span id="workplace-type">Remote</span></div><div class="meta-item"><span class="meta-label">Employment:</span> <span id="employment-type">Full-time</span></div><div class="meta-item"><span class="meta-label">Posted:</span> <span id="posted-time">2 days ago</span></div><div class="meta-item"><span class="meta-label">Applicants:</span> <span id="applicants">50+ applicants</span></div></div><div class="apply-section"><a id="apply-link" href="#" target="_blank" class="btn btn-outline">View Job on LinkedIn </a><span id="easy-apply" class="easy-apply-badge" style="display: none;">Easy Apply</span></div><div class="job-description"><button class="description-toggle" id="description-toggle"><span>Job Description</span> <svg class="toggle-icon" width="12" height="12" viewBox="0 0 24 24" fill="currentColor"><path d="M7 10l5 5 5-5z"/></svg></button><div class="description-content" id="description-content" style="display: none;"><div class="description-section"><h4>Summary</h4><p id="job-summary">Job summary will appear here...</p></div><div class="description-section"><h4>Responsibilities</h4><ul id="job-responsibilities"></ul></div><div class="description-section"><h4>Requirements</h4><ul id="job-requirements"></ul></div><div class="description-section"><h4>Benefits</h4><ul id="job-benefits"></ul></div></div></div><div class="action-section"><button id="add-job-btn" class="btn btn-primary"><svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg> Save to Job Tracker</button> <small class="action-help">Saved jobs can be viewed at <a href="http://localhost:3001" target="_blank">localhost:3001</a></small></div><div class="portal-connection"><div id="portal-status" class="portal-status"><div class="status-indicator"></div><span>Checking portal...</span></div><small class="portal-note">Jobs are automatically synced when portal is connected</small></div></div></div><div id="status-messages"></div></div><div id="error-state" class="error-container" style="display: none;"><div class="error-icon">⚠️</div><h3>Something went wrong</h3><p id="error-message">An error occurred while loading the extension.</p><button id="retry-btn" class="btn btn-primary">Retry</button></div></div><script src="popup.js"></script></body></html>