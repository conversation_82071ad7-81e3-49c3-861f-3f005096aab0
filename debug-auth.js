// Debug script for LinkedIn authentication issues
// Run this in the browser console to test authentication flow

async function debugLinkedInAuth() {
  console.log('=== LinkedIn Authentication Debug ===');
  
  // Test 1: Check extension permissions
  console.log('1. Checking extension permissions...');
  try {
    const permissions = await chrome.permissions.getAll();
    console.log('Extension permissions:', permissions);
  } catch (error) {
    console.error('Failed to get permissions:', error);
  }
  
  // Test 2: Check redirect URI
  console.log('2. Checking redirect URI...');
  try {
    const redirectUri = chrome.identity.getRedirectURL();
    console.log('Redirect URI:', redirectUri);
  } catch (error) {
    console.error('Failed to get redirect URI:', error);
  }
  
  // Test 3: Test LinkedIn authorization URL
  console.log('3. Testing LinkedIn authorization URL...');
  const clientId = '772ojq74j5zy0p';
  const redirectUri = chrome.identity.getRedirectURL();
  const scope = 'r_basicprofile r_emailaddress';
  const state = Math.random().toString(36).substring(2, 15);
  
  const authUrl = `https://www.linkedin.com/oauth/v2/authorization?` +
    `response_type=code&` +
    `client_id=${clientId}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `scope=${encodeURIComponent(scope)}&` +
    `state=${state}`;
  
  console.log('Authorization URL:', authUrl);
  
  // Test 4: Try to access LinkedIn directly
  console.log('4. Testing LinkedIn API access...');
  try {
    const response = await fetch('https://www.linkedin.com/oauth/v2/authorization', {
      method: 'HEAD',
      mode: 'no-cors'
    });
    console.log('LinkedIn API accessible');
  } catch (error) {
    console.error('LinkedIn API not accessible:', error);
  }
  
  // Test 5: Check if we can launch web auth flow with a simple URL
  console.log('5. Testing basic web auth flow...');
  try {
    const testUrl = 'https://www.linkedin.com/';
    const result = await chrome.identity.launchWebAuthFlow({
      url: testUrl,
      interactive: false
    });
    console.log('Basic web auth flow works:', result);
  } catch (error) {
    console.error('Basic web auth flow failed:', error);
  }
  
  console.log('=== Debug Complete ===');
}

// Alternative authentication method using chrome.identity.getAuthToken
async function tryAlternativeAuth() {
  console.log('=== Trying Alternative Authentication ===');
  
  try {
    // This requires oauth2 configuration in manifest.json
    const token = await chrome.identity.getAuthToken({ interactive: true });
    console.log('Alternative auth token:', token);
    return token;
  } catch (error) {
    console.error('Alternative auth failed:', error);
    return null;
  }
}

// Manual LinkedIn OAuth flow
async function manualLinkedInAuth() {
  console.log('=== Manual LinkedIn OAuth Flow ===');
  
  const clientId = '772ojq74j5zy0p';
  const redirectUri = chrome.identity.getRedirectURL();
  const scope = 'r_basicprofile r_emailaddress';
  const state = Math.random().toString(36).substring(2, 15);
  
  const authUrl = `https://www.linkedin.com/oauth/v2/authorization?` +
    `response_type=code&` +
    `client_id=${clientId}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `scope=${encodeURIComponent(scope)}&` +
    `state=${state}`;
  
  console.log('Opening LinkedIn authorization page...');
  console.log('Auth URL:', authUrl);
  
  try {
    const responseUrl = await chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    });
    
    console.log('Response URL:', responseUrl);
    
    if (responseUrl) {
      const urlParams = new URLSearchParams(new URL(responseUrl).search);
      const code = urlParams.get('code');
      const error = urlParams.get('error');
      const errorDescription = urlParams.get('error_description');
      
      if (error) {
        console.error('OAuth error:', error, errorDescription);
      } else if (code) {
        console.log('Authorization code received:', code);
      } else {
        console.log('No code or error in response');
      }
    }
  } catch (error) {
    console.error('Manual auth failed:', error);
  }
}

// Export functions for console use
if (typeof window !== 'undefined') {
  window.debugLinkedInAuth = debugLinkedInAuth;
  window.tryAlternativeAuth = tryAlternativeAuth;
  window.manualLinkedInAuth = manualLinkedInAuth;
}
