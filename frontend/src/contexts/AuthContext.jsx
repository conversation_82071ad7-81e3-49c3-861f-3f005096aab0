import React, { createContext, useContext, useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'
import api from '../utils/api'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const queryClient = useQueryClient()

  // Check if user is authenticated on app load
  const { data: authData, isLoading: authLoading } = useQuery(
    'auth-check',
    async () => {
      const token = localStorage.getItem('access_token')
      if (!token) {
        throw new Error('No token found')
      }
      
      const response = await api.get('/auth/me')
      return response.data
    },
    {
      retry: false,
      onSuccess: (data) => {
        setUser(data.user)
        setLoading(false)
      },
      onError: () => {
        localStorage.removeItem('access_token')
        setUser(null)
        setLoading(false)
      }
    }
  )

  // LinkedIn authentication mutation
  const loginMutation = useMutation(
    async (authCode) => {
      const response = await api.post('/auth/linkedin', {
        code: authCode,
        redirect_uri: `${window.location.origin}/auth/linkedin/callback`,
        source: 'portal'
      })
      return response.data
    },
    {
      onSuccess: (data) => {
        localStorage.setItem('access_token', data.access_token)
        setUser(data.user)
        queryClient.invalidateQueries('auth-check')
        toast.success(`Welcome back, ${data.user.first_name}!`)
      },
      onError: (error) => {
        console.error('Login failed:', error)
        toast.error(error.response?.data?.error || 'Login failed')
      }
    }
  )

  // Logout mutation
  const logoutMutation = useMutation(
    async () => {
      await api.post('/auth/logout')
    },
    {
      onSuccess: () => {
        localStorage.removeItem('access_token')
        setUser(null)
        queryClient.clear()
        toast.success('Logged out successfully')
      },
      onError: (error) => {
        // Still clear local state even if API call fails
        localStorage.removeItem('access_token')
        setUser(null)
        queryClient.clear()
        console.error('Logout error:', error)
      }
    }
  )

  // LinkedIn OAuth flow
  const initiateLinkedInLogin = () => {
    const clientId = import.meta.env.VITE_LINKEDIN_CLIENT_ID || '772ojq74j5zy0p'
    const redirectUri = encodeURIComponent(`${window.location.origin}/auth/linkedin/callback`)
    const scope = encodeURIComponent('r_liteprofile r_emailaddress')
    const state = Math.random().toString(36).substring(2, 15)
    
    // Store state for verification
    sessionStorage.setItem('linkedin_oauth_state', state)
    
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?` +
      `response_type=code&` +
      `client_id=${clientId}&` +
      `redirect_uri=${redirectUri}&` +
      `scope=${scope}&` +
      `state=${state}`
    
    window.location.href = authUrl
  }

  // Handle LinkedIn OAuth callback
  const handleLinkedInCallback = async (code, state) => {
    try {
      // Verify state parameter
      const storedState = sessionStorage.getItem('linkedin_oauth_state')
      if (state !== storedState) {
        throw new Error('Invalid state parameter')
      }
      
      sessionStorage.removeItem('linkedin_oauth_state')
      await loginMutation.mutateAsync(code)
    } catch (error) {
      console.error('LinkedIn callback error:', error)
      toast.error('Authentication failed')
    }
  }

  // Refresh token
  const refreshToken = async () => {
    try {
      const response = await api.post('/auth/refresh')
      localStorage.setItem('access_token', response.data.access_token)
      return response.data.access_token
    } catch (error) {
      localStorage.removeItem('access_token')
      setUser(null)
      throw error
    }
  }

  // Update user profile
  const updateProfile = useMutation(
    async (profileData) => {
      const response = await api.put('/users/profile', profileData)
      return response.data
    },
    {
      onSuccess: (data) => {
        setUser(data.user)
        queryClient.invalidateQueries('auth-check')
        toast.success('Profile updated successfully')
      },
      onError: (error) => {
        toast.error(error.response?.data?.error || 'Failed to update profile')
      }
    }
  )

  // Check if extension is connected (for showing connection status)
  const checkExtensionConnection = async () => {
    try {
      // This would be called by the extension to verify portal login
      const response = await api.get('/auth/verify')
      return response.data
    } catch (error) {
      return { success: false, connected: false }
    }
  }

  useEffect(() => {
    // Handle LinkedIn OAuth callback
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')
    const error = urlParams.get('error')

    if (error) {
      toast.error('LinkedIn authentication was cancelled or failed')
      window.history.replaceState({}, document.title, window.location.pathname)
      setLoading(false)
      return
    }

    if (code && state) {
      handleLinkedInCallback(code, state)
      window.history.replaceState({}, document.title, window.location.pathname)
      return
    }

    // If no OAuth callback, check existing auth
    if (!authLoading && !authData) {
      setLoading(false)
    }
  }, [authLoading, authData])

  const value = {
    user,
    loading: loading || authLoading || loginMutation.isLoading,
    login: initiateLinkedInLogin,
    logout: logoutMutation.mutate,
    updateProfile: updateProfile.mutate,
    refreshToken,
    checkExtensionConnection,
    isLoggingIn: loginMutation.isLoading,
    isLoggingOut: logoutMutation.isLoading,
    isUpdatingProfile: updateProfile.isLoading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
